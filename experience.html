<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pengalaman - Situs Web Pribadi</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/responsive.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <a href="index.html">Portfolio</a>
        </div>
        <div class="nav-menu" id="nav-menu">
          <a href="index.html" class="nav-link">Beranda</a>
          <a href="about.html" class="nav-link">Tentang</a>
          <a href="portfolio.html" class="nav-link">Portfolio</a>
          <a href="skills.html" class="nav-link">Keahlian</a>
          <a href="experience.html" class="nav-link active">Pengalaman</a>
          <a href="organisasi.html" class="nav-link">Organisasi</a>
          <a href="blog.html" class="nav-link">Blog</a>
          <a href="gallery.html" class="nav-link">Galeri</a>
          <a href="contact.html" class="nav-link">Kontak</a>
        </div>
        <div class="nav-toggle" id="nav-toggle">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </div>
        <div class="theme-toggle" id="theme-toggle">
          <i class="fas fa-moon"></i>
        </div>
      </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">Pengalaman</h1>
        <p class="page-subtitle">
          Perjalanan karir dan pendidikan yang membentuk keahlian saya
        </p>
      </div>
    </section>

    <!-- Experience Timeline -->
    <section class="experience-section">
      <div class="container">
        <div class="experience-tabs">
          <button class="tab-btn active" data-tab="work">
            Pengalaman Kerja
          </button>
          <button class="tab-btn" data-tab="education">Pendidikan</button>
          <button class="tab-btn" data-tab="certifications">Sertifikasi</button>
        </div>

        <!-- Work Experience -->
        <div class="tab-content active" id="work">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-date">2022 - Sekarang</div>
              <div class="timeline-content">
                <h3>Senior Full Stack Developer</h3>
                <h4>PT. Digital Innovation</h4>
                <p>
                  Memimpin tim pengembangan untuk proyek-proyek enterprise dan
                  startup. Bertanggung jawab dalam arsitektur sistem, code
                  review, dan mentoring junior developer.
                </p>
                <ul class="timeline-achievements">
                  <li>
                    Mengembangkan platform e-commerce yang melayani 100K+
                    pengguna
                  </li>
                  <li>
                    Meningkatkan performa aplikasi hingga 40% melalui optimasi
                  </li>
                  <li>
                    Memimpin migrasi sistem legacy ke arsitektur microservices
                  </li>
                  <li>
                    Mentoring 5 junior developer dan meningkatkan produktivitas
                    tim 30%
                  </li>
                </ul>
                <div class="timeline-skills">
                  <span class="skill-tag">React</span>
                  <span class="skill-tag">Node.js</span>
                  <span class="skill-tag">AWS</span>
                  <span class="skill-tag">Docker</span>
                  <span class="skill-tag">MongoDB</span>
                </div>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-date">2020 - 2022</div>
              <div class="timeline-content">
                <h3>Frontend Developer</h3>
                <h4>PT. Tech Solutions</h4>
                <p>
                  Mengembangkan antarmuka pengguna yang responsif dan interaktif
                  untuk berbagai aplikasi web. Berkolaborasi dengan tim design
                  dan backend untuk menghasilkan produk berkualitas.
                </p>
                <ul class="timeline-achievements">
                  <li>Mengembangkan 15+ website dan aplikasi web</li>
                  <li>Implementasi design system yang konsisten</li>
                  <li>
                    Meningkatkan conversion rate hingga 25% melalui UX
                    optimization
                  </li>
                  <li>Mengintegrasikan API dan third-party services</li>
                </ul>
                <div class="timeline-skills">
                  <span class="skill-tag">Vue.js</span>
                  <span class="skill-tag">JavaScript</span>
                  <span class="skill-tag">CSS3</span>
                  <span class="skill-tag">Figma</span>
                  <span class="skill-tag">Git</span>
                </div>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-date">2019 - 2020</div>
              <div class="timeline-content">
                <h3>Junior Web Developer</h3>
                <h4>CV. Creative Digital</h4>
                <p>
                  Memulai karir sebagai web developer dengan fokus pada
                  pengembangan website company profile dan landing pages.
                  Belajar best practices dalam web development.
                </p>
                <ul class="timeline-achievements">
                  <li>Mengembangkan 20+ website company profile</li>
                  <li>Belajar framework modern dan tools development</li>
                  <li>
                    Berpartisipasi dalam project management dan client
                    communication
                  </li>
                  <li>Meningkatkan skill dalam responsive design</li>
                </ul>
                <div class="timeline-skills">
                  <span class="skill-tag">HTML5</span>
                  <span class="skill-tag">CSS3</span>
                  <span class="skill-tag">JavaScript</span>
                  <span class="skill-tag">PHP</span>
                  <span class="skill-tag">MySQL</span>
                </div>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-date">2018 - 2019</div>
              <div class="timeline-content">
                <h3>Freelance Web Developer</h3>
                <h4>Freelancer</h4>
                <p>
                  Memulai sebagai freelancer untuk mengembangkan portfolio dan
                  pengalaman. Menangani berbagai proyek dari UMKM hingga startup
                  kecil.
                </p>
                <ul class="timeline-achievements">
                  <li>Menyelesaikan 10+ proyek freelance</li>
                  <li>Membangun network dan client base</li>
                  <li>Belajar project management dan client relations</li>
                  <li>Mengembangkan skill dalam berbagai teknologi</li>
                </ul>
                <div class="timeline-skills">
                  <span class="skill-tag">WordPress</span>
                  <span class="skill-tag">HTML</span>
                  <span class="skill-tag">CSS</span>
                  <span class="skill-tag">JavaScript</span>
                  <span class="skill-tag">Photoshop</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Education -->
        <div class="tab-content" id="education">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-date">2015 - 2019</div>
              <div class="timeline-content">
                <h3>S1 Teknik Informatika</h3>
                <h4>Universitas Teknologi Indonesia</h4>
                <p>
                  Menyelesaikan pendidikan sarjana dengan fokus pada
                  pengembangan perangkat lunak dan sistem informasi. IPK:
                  3.75/4.00
                </p>
                <ul class="timeline-achievements">
                  <li>Juara 2 Kompetisi Programming tingkat nasional</li>
                  <li>Ketua Himpunan Mahasiswa Teknik Informatika</li>
                  <li>
                    Skripsi: "Sistem Rekomendasi E-Commerce menggunakan Machine
                    Learning"
                  </li>
                  <li>Aktif dalam organisasi dan kegiatan kampus</li>
                </ul>
                <div class="timeline-skills">
                  <span class="skill-tag">Java</span>
                  <span class="skill-tag">Python</span>
                  <span class="skill-tag">Database</span>
                  <span class="skill-tag">Algorithm</span>
                  <span class="skill-tag">Data Structure</span>
                </div>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-date">2012 - 2015</div>
              <div class="timeline-content">
                <h3>SMA Negeri 1 Jakarta</h3>
                <h4>Jurusan IPA</h4>
                <p>
                  Menyelesaikan pendidikan menengah atas dengan prestasi
                  akademik yang baik dan aktif dalam berbagai kegiatan
                  ekstrakurikuler.
                </p>
                <ul class="timeline-achievements">
                  <li>Ranking 5 besar di kelas</li>
                  <li>Ketua OSIS periode 2014-2015</li>
                  <li>Juara 1 Olimpiade Matematika tingkat kota</li>
                  <li>Aktif dalam klub komputer dan robotika</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- Certifications -->
        <div class="tab-content" id="certifications">
          <div class="certifications-grid">
            <div class="cert-item">
              <div class="cert-icon">
                <i class="fab fa-aws"></i>
              </div>
              <h3>AWS Certified Solutions Architect</h3>
              <p>Amazon Web Services</p>
              <span class="cert-date">2023</span>
            </div>

            <div class="cert-item">
              <div class="cert-icon">
                <i class="fab fa-google"></i>
              </div>
              <h3>Google Cloud Professional Developer</h3>
              <p>Google Cloud Platform</p>
              <span class="cert-date">2023</span>
            </div>

            <div class="cert-item">
              <div class="cert-icon">
                <i class="fab fa-react"></i>
              </div>
              <h3>React Developer Certification</h3>
              <p>Meta (Facebook)</p>
              <span class="cert-date">2022</span>
            </div>

            <div class="cert-item">
              <div class="cert-icon">
                <i class="fas fa-mobile-alt"></i>
              </div>
              <h3>Mobile App Development</h3>
              <p>Coursera - University of Toronto</p>
              <span class="cert-date">2022</span>
            </div>

            <div class="cert-item">
              <div class="cert-icon">
                <i class="fas fa-palette"></i>
              </div>
              <h3>UX Design Professional</h3>
              <p>Google UX Design Certificate</p>
              <span class="cert-date">2021</span>
            </div>

            <div class="cert-item">
              <div class="cert-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <h3>Cybersecurity Fundamentals</h3>
              <p>IBM Security</p>
              <span class="cert-date">2021</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>Muhammad Azmi</h3>
            <p>
              Web Developer & UI/UX Designer yang berpengalaman dalam
              menciptakan solusi digital inovatif.
            </p>
            <div class="footer-social">
              <a href="#" class="social-link"><i class="fab fa-github"></i></a>
              <a href="#" class="social-link"
                ><i class="fab fa-linkedin"></i
              ></a>
              <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
              <a href="#" class="social-link"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-section">
            <h4>Menu</h4>
            <ul class="footer-links">
              <li><a href="index.html">Beranda</a></li>
              <li><a href="about.html">Tentang</a></li>
              <li><a href="portfolio.html">Portfolio</a></li>
              <li><a href="contact.html">Kontak</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Layanan</h4>
            <ul class="footer-links">
              <li><a href="services.html">Web Development</a></li>
              <li><a href="services.html">UI/UX Design</a></li>
              <li><a href="services.html">Mobile App</a></li>
              <li><a href="services.html">Konsultasi</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Kontak</h4>
            <div class="contact-info">
              <p><i class="fas fa-envelope"></i> <EMAIL></p>
              <p><i class="fas fa-phone"></i> +62 822 8629 3850</p>
              <p><i class="fas fa-map-marker-alt"></i> Pekanbaru, Indonesia</p>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Muhammad Azmi. Semua hak dilindungi.</p>
        </div>
      </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top">
      <i class="fas fa-chevron-up"></i>
    </button>

    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/experience.js"></script>
  </body>
</html>
