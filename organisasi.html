<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Organisasi - <PERSON></title>
    <meta
      name="description"
      content="Pengalaman organisasi Muhammad <PERSON> selama di kampus"
    />
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/responsive.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <a href="index.html">Portfolio</a>
        </div>
        <div class="nav-menu" id="nav-menu">
          <a href="index.html" class="nav-link">Be<PERSON>a</a>
          <a href="about.html" class="nav-link">Tentang</a>
          <a href="portfolio.html" class="nav-link">Portfolio</a>
          <a href="skills.html" class="nav-link">Keahlian</a>
          <a href="experience.html" class="nav-link">Pengalaman</a>
          <a href="organisasi.html" class="nav-link active">Organisasi</a>
          <a href="blog.html" class="nav-link">Blog</a>
          <a href="gallery.html" class="nav-link">Galeri</a>
          <a href="contact.html" class="nav-link">Kontak</a>
        </div>
        <div class="nav-toggle" id="nav-toggle">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </div>
      </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">Pengalaman Organisasi</h1>
        <p class="page-subtitle">
          Perjalanan kepemimpinan dan kontribusi saya dalam berbagai organisasi
          kampus
        </p>
      </div>
    </section>

    <!-- Organization Experience -->
    <section class="organization-section">
      <div class="container">
        <div class="organization-intro">
          <h2 class="section-title">Pengalaman Berorganisasi</h2>
          <p class="section-description">
            Selama masa perkuliahan, saya aktif terlibat dalam berbagai
            organisasi yang telah membentuk karakter kepemimpinan dan kemampuan
            berkolaborasi. Berikut adalah pengalaman organisasi yang telah saya
            jalani.
          </p>
        </div>

        <div class="organization-timeline">
          <!-- Organization 1 -->
          <div class="organization-item">
            <div class="organization-image">
              <img
                src="images/humas.jpg"
                alt="Kepala Departemen Hubungan Masyarakat"
              />
              <div class="organization-overlay">
                <div class="organization-badge">
                  <i class="fas fa-users"></i>
                </div>
              </div>
            </div>
            <div class="organization-content">
              <div class="organization-header">
                <h3>Kepala Departemen Hubungan Masyarakat</h3>
                <span class="organization-period">2023 - 2024</span>
              </div>
              <div class="organization-details">
                <p class="organization-description">
                  Memimpin departemen hubungan masyarakat dalam menjalin
                  komunikasi dan kerjasama dengan berbagai pihak eksternal.
                  Bertanggung jawab dalam strategi komunikasi dan publikasi
                  kegiatan organisasi.
                </p>
                <div class="organization-responsibilities">
                  <h4>Tanggung Jawab:</h4>
                  <ul>
                    <li>Mengelola strategi komunikasi eksternal organisasi</li>
                    <li>
                      Membangun dan memelihara hubungan dengan media dan
                      stakeholder
                    </li>
                    <li>
                      Mengkoordinasi tim dalam pelaksanaan program publikasi
                    </li>
                    <li>
                      Merancang konten komunikasi yang efektif dan menarik
                    </li>
                  </ul>
                </div>
                <div class="organization-achievements">
                  <h4>Pencapaian:</h4>
                  <ul>
                    <li>
                      Meningkatkan engagement media sosial organisasi sebesar
                      150%
                    </li>
                    <li>Berhasil menjalin kerjasama dengan 5 media lokal</li>
                    <li>Mengorganisir 10+ event publikasi yang sukses</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Organization 2 -->
          <div class="organization-item">
            <div class="organization-image">
              <img src="images/aso.jpg" alt="Wakil Ketua Asomatif 2024" />
              <div class="organization-overlay">
                <div class="organization-badge">
                  <i class="fas fa-crown"></i>
                </div>
              </div>
            </div>
            <div class="organization-content">
              <div class="organization-header">
                <h3>Wakil Ketua Asomatif 2024</h3>
                <span class="organization-period">2024</span>
              </div>
              <div class="organization-details">
                <p class="organization-description">
                  Menjabat sebagai Wakil Ketua Asosiasi Mahasiswa Informatika,
                  membantu memimpin organisasi mahasiswa dalam mengembangkan
                  potensi akademik dan non-akademik anggota.
                </p>
                <div class="organization-responsibilities">
                  <h4>Tanggung Jawab:</h4>
                  <ul>
                    <li>
                      Membantu ketua dalam menjalankan visi misi organisasi
                    </li>
                    <li>Mengkoordinasi berbagai divisi dalam organisasi</li>
                    <li>Memimpin rapat koordinasi dan evaluasi program</li>
                    <li>
                      Menjadi penghubung antara mahasiswa dan pihak akademik
                    </li>
                  </ul>
                </div>
                <div class="organization-achievements">
                  <h4>Pencapaian:</h4>
                  <ul>
                    <li>Menginisiasi program mentoring untuk mahasiswa baru</li>
                    <li>Mengorganisir seminar teknologi dengan 200+ peserta</li>
                    <li>
                      Meningkatkan partisipasi mahasiswa dalam kegiatan
                      organisasi
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Organization 3 -->
          <div class="organization-item">
            <div class="organization-image">
              <img
                src="images/korda.jpg"
                alt="Koordinator Daerah Riau Permikomnas Wilayah 2"
              />
              <div class="organization-overlay">
                <div class="organization-badge">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
              </div>
            </div>
            <div class="organization-content">
              <div class="organization-header">
                <h3>Koordinator Daerah Riau Permikomnas Wilayah 2</h3>
                <span class="organization-period">2023 - Sekarang</span>
              </div>
              <div class="organization-details">
                <p class="organization-description">
                  Berperan sebagai koordinator daerah untuk Perhimpunan
                  Mahasiswa Ilmu Komputer Nasional wilayah Riau, menghubungkan
                  mahasiswa informatika se-Riau dalam satu wadah organisasi
                  nasional.
                </p>
                <div class="organization-responsibilities">
                  <h4>Tanggung Jawab:</h4>
                  <ul>
                    <li>Mengkoordinasi kegiatan Permikomnas di wilayah Riau</li>
                    <li>Memfasilitasi komunikasi antar universitas di Riau</li>
                    <li>Mengorganisir event dan kompetisi tingkat regional</li>
                    <li>Menjadi perwakilan wilayah dalam kegiatan nasional</li>
                  </ul>
                </div>
                <div class="organization-achievements">
                  <h4>Pencapaian:</h4>
                  <ul>
                    <li>
                      Menghubungkan 8 universitas di Riau dalam jaringan
                      Permikomnas
                    </li>
                    <li>Mengorganisir kompetisi programming tingkat Riau</li>
                    <li>
                      Memfasilitasi pertukaran mahasiswa antar universitas
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Skills Gained -->
    <section class="skills-gained">
      <div class="container">
        <h2 class="section-title">Keterampilan yang Diperoleh</h2>
        <div class="skills-grid">
          <div class="skill-item">
            <div class="skill-icon">
              <i class="fas fa-users-cog"></i>
            </div>
            <h3>Kepemimpinan</h3>
            <p>
              Kemampuan memimpin tim dan mengambil keputusan strategis dalam
              berbagai situasi
            </p>
          </div>
          <div class="skill-item">
            <div class="skill-icon">
              <i class="fas fa-comments"></i>
            </div>
            <h3>Komunikasi</h3>
            <p>
              Keterampilan komunikasi efektif dengan berbagai stakeholder dan
              media
            </p>
          </div>
          <div class="skill-item">
            <div class="skill-icon">
              <i class="fas fa-project-diagram"></i>
            </div>
            <h3>Manajemen Proyek</h3>
            <p>
              Pengalaman mengelola proyek dan event dari perencanaan hingga
              eksekusi
            </p>
          </div>
          <div class="skill-item">
            <div class="skill-icon">
              <i class="fas fa-handshake"></i>
            </div>
            <h3>Networking</h3>
            <p>
              Kemampuan membangun dan memelihara jaringan profesional yang luas
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Custom CSS for Organization Page -->
    <style>
      .organization-section {
        padding: 80px 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      }

      .organization-intro {
        text-align: center;
        margin-bottom: 60px;
      }

      .section-description {
        font-size: 1.1rem;
        color: #6c757d;
        max-width: 800px;
        margin: 0 auto;
        line-height: 1.6;
      }

      .organization-timeline {
        max-width: 1000px;
        margin: 0 auto;
      }

      .organization-item {
        display: flex;
        gap: 40px;
        margin-bottom: 60px;
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .organization-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      .organization-image {
        flex: 0 0 300px;
        position: relative;
        border-radius: 10px;
        overflow: hidden;
      }

      .organization-image img {
        width: 100%;
        height: 250px;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .organization-item:hover .organization-image img {
        transform: scale(1.05);
      }

      .organization-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          rgba(74, 144, 226, 0.8),
          rgba(80, 200, 120, 0.8)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .organization-item:hover .organization-overlay {
        opacity: 1;
      }

      .organization-badge {
        background: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #4a90e2;
      }

      .organization-content {
        flex: 1;
      }

      .organization-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;
      }

      .organization-header h3 {
        font-size: 1.5rem;
        color: #2c3e50;
        margin: 0;
        line-height: 1.3;
      }

      .organization-period {
        background: linear-gradient(45deg, #4a90e2, #50c878);
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        white-space: nowrap;
      }

      .organization-description {
        color: #6c757d;
        line-height: 1.6;
        margin-bottom: 25px;
      }

      .organization-responsibilities,
      .organization-achievements {
        margin-bottom: 20px;
      }

      .organization-responsibilities h4,
      .organization-achievements h4 {
        color: #2c3e50;
        font-size: 1.1rem;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .organization-responsibilities h4:before {
        content: "📋";
        font-size: 1rem;
      }

      .organization-achievements h4:before {
        content: "🏆";
        font-size: 1rem;
      }

      .organization-responsibilities ul,
      .organization-achievements ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .organization-responsibilities li,
      .organization-achievements li {
        padding: 5px 0;
        padding-left: 20px;
        position: relative;
        color: #6c757d;
        line-height: 1.5;
      }

      .organization-responsibilities li:before {
        content: "▸";
        position: absolute;
        left: 0;
        color: #4a90e2;
        font-weight: bold;
      }

      .organization-achievements li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #50c878;
        font-weight: bold;
      }

      .skills-gained {
        padding: 80px 0;
        background: white;
      }

      .skills-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        margin-top: 50px;
      }

      .skill-item {
        text-align: center;
        padding: 30px 20px;
        background: #f8f9fa;
        border-radius: 15px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .skill-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }

      .skill-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(45deg, #4a90e2, #50c878);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 2rem;
        color: white;
      }

      .skill-item h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.3rem;
      }

      .skill-item p {
        color: #6c757d;
        line-height: 1.6;
      }

      @media (max-width: 768px) {
        .organization-item {
          flex-direction: column;
          gap: 20px;
        }

        .organization-image {
          flex: none;
        }

        .organization-header {
          flex-direction: column;
          gap: 10px;
        }

        .organization-header h3 {
          font-size: 1.3rem;
        }

        .skills-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>

    <!-- Call to Action -->
    <section class="cta">
      <div class="container">
        <div class="cta-content">
          <h2>Mari Berkolaborasi!</h2>
          <p>
            Pengalaman organisasi telah membentuk saya menjadi individu yang
            siap berkolaborasi dan berkontribusi. Mari diskusikan bagaimana kita
            bisa bekerja sama.
          </p>
          <div class="cta-buttons">
            <a href="contact.html" class="btn btn-primary">Hubungi Saya</a>
            <a href="portfolio.html" class="btn btn-secondary"
              >Lihat Portfolio</a
            >
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>Muhammad Azmi</h3>
            <p>
              Web Developer & UI/UX Designer yang berpengalaman dalam
              menciptakan solusi digital inovatif.
            </p>
            <div class="footer-social">
              <a href="#" class="social-link"><i class="fab fa-github"></i></a>
              <a href="#" class="social-link"
                ><i class="fab fa-linkedin"></i
              ></a>
              <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
              <a href="#" class="social-link"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-section">
            <h4>Menu</h4>
            <ul class="footer-links">
              <li><a href="index.html">Beranda</a></li>
              <li><a href="about.html">Tentang</a></li>
              <li><a href="portfolio.html">Portfolio</a></li>
              <li><a href="contact.html">Kontak</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Layanan</h4>
            <ul class="footer-links">
              <li><a href="services.html">Web Development</a></li>
              <li><a href="services.html">UI/UX Design</a></li>
              <li><a href="services.html">Mobile App</a></li>
              <li><a href="services.html">Konsultasi</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Kontak</h4>
            <div class="contact-info">
              <p><i class="fas fa-envelope"></i> <EMAIL></p>
              <p><i class="fas fa-phone"></i> +62 822 8629 3850</p>
              <p><i class="fas fa-map-marker-alt"></i> Pekanbaru, Indonesia</p>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Muhammad Azmi. Semua hak dilindungi.</p>
        </div>
      </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top">
      <i class="fas fa-chevron-up"></i>
    </button>

    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
  </body>
</html>
