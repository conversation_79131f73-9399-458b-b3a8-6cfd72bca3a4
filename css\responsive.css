/* Responsive Design */

/* Large screens */
@media (max-width: 1200px) {
    .container {
        padding: 0 30px;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .image-container {
        width: 350px;
        height: 350px;
    }
}

/* Medium screens */
@media (max-width: 992px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-primary);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-medium);
        padding: 2rem 0;
        gap: 1rem;
    }

    [data-theme="dark"] .nav-menu {
        background-color: var(--bg-primary);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .image-container {
        width: 300px;
        height: 300px;
        margin: 0 auto;
    }

    .about-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .work-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .page-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }
}

/* Small screens */
@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }

    .nav-container {
        padding: 0 20px;
    }

    .hero {
        padding: 80px 0 30px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }

    .image-container {
        width: 250px;
        height: 250px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-item {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .work-grid {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .interests-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .page-header {
        padding: 100px 0 60px;
    }

    .page-title {
        font-size: 2rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .about-text h2 {
        font-size: 2rem;
    }

    .info-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: left;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-content p {
        font-size: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .nav-container {
        padding: 0 15px;
        height: 60px;
    }

    .nav-logo a {
        font-size: 1.3rem;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .image-container {
        width: 200px;
        height: 200px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .interests-grid {
        grid-template-columns: 1fr;
    }

    .interest-item {
        padding: 1rem;
    }

    .page-title {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .about-text h2 {
        font-size: 1.8rem;
    }

    .value-item {
        padding: 1.5rem;
    }

    .value-icon {
        width: 60px;
        height: 60px;
    }

    .value-icon i {
        font-size: 1.5rem;
    }

    .cta-content h2 {
        font-size: 1.8rem;
    }

    .hero-social {
        justify-content: center;
    }

    .social-link {
        width: 40px;
        height: 40px;
    }
}

/* Landscape orientation for mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        min-height: auto;
        padding: 100px 0 50px;
    }

    .page-header {
        padding: 100px 0 50px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-image img,
    .about-image img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print styles */
@media print {
    .navbar,
    .back-to-top,
    .hero-buttons,
    .cta,
    .footer {
        display: none !important;
    }

    .hero {
        min-height: auto;
        padding: 2rem 0;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    .page-header {
        background: none;
        color: #000;
        padding: 1rem 0;
    }

    .section-title {
        font-size: 18pt;
        margin-bottom: 1rem;
    }

    .work-item,
    .stat-item,
    .value-item {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .scroll-indicator {
        animation: none;
    }

    .spinner {
        animation: none;
        border: 4px solid var(--primary-color);
    }
}

/* Dark mode specific responsive adjustments */
@media (max-width: 992px) {
    [data-theme="dark"] .nav-menu {
        background-color: var(--bg-primary);
        border-top: 1px solid var(--border-color);
    }
}

/* Focus styles for accessibility */
@media (max-width: 768px) {
    .nav-link:focus,
    .btn:focus,
    .social-link:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }

    .nav-toggle:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
        border-radius: 4px;
    }
}
