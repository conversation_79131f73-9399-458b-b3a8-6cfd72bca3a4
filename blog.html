<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Blog - Situs Web Pribadi</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/responsive.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <a href="index.html">Portfolio</a>
        </div>
        <div class="nav-menu" id="nav-menu">
          <a href="index.html" class="nav-link">Beranda</a>
          <a href="about.html" class="nav-link">Tentang</a>
          <a href="portfolio.html" class="nav-link">Portfolio</a>
          <a href="skills.html" class="nav-link">Keahlian</a>
          <a href="experience.html" class="nav-link">Pengalaman</a>
          <a href="organisasi.html" class="nav-link">Organisasi</a>
          <a href="blog.html" class="nav-link active">Blog</a>
          <a href="gallery.html" class="nav-link">Galeri</a>
          <a href="contact.html" class="nav-link">Kontak</a>
        </div>
        <div class="nav-toggle" id="nav-toggle">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </div>
        <div class="theme-toggle" id="theme-toggle">
          <i class="fas fa-moon"></i>
        </div>
      </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">Blog</h1>
        <p class="page-subtitle">
          Berbagi pengetahuan dan pengalaman dalam dunia teknologi
        </p>
      </div>
    </section>

    <!-- Blog Filter -->
    <section class="blog-filter">
      <div class="container">
        <div class="filter-buttons">
          <button class="filter-btn active" data-filter="all">Semua</button>
          <button class="filter-btn" data-filter="web-development">
            Web Development
          </button>
          <button class="filter-btn" data-filter="mobile">
            Mobile Development
          </button>
          <button class="filter-btn" data-filter="design">UI/UX Design</button>
          <button class="filter-btn" data-filter="tutorial">Tutorial</button>
          <button class="filter-btn" data-filter="tips">Tips & Tricks</button>
        </div>
      </div>
    </section>

    <!-- Blog Posts -->
    <section class="blog-section">
      <div class="container">
        <div class="blog-grid">
          <article class="blog-post" data-category="web-development">
            <div class="blog-image">
              <img src="images/azmi.jpg" alt="React Hooks" />
              <div class="blog-category">Web Development</div>
            </div>
            <div class="blog-content">
              <div class="blog-meta">
                <span class="blog-date"
                  ><i class="fas fa-calendar"></i> 15 Januari 2024</span
                >
                <span class="blog-read-time"
                  ><i class="fas fa-clock"></i> 5 min read</span
                >
              </div>
              <h2>Memahami React Hooks: useState dan useEffect</h2>
              <p>
                React Hooks telah mengubah cara kita menulis komponen React.
                Dalam artikel ini, kita akan membahas dua hooks yang paling
                sering digunakan: useState dan useEffect.
              </p>
              <div class="blog-tags">
                <span class="tag">React</span>
                <span class="tag">JavaScript</span>
                <span class="tag">Frontend</span>
              </div>
              <a href="#" class="blog-read-more"
                >Baca Selengkapnya <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </article>

          <article class="blog-post" data-category="design">
            <div class="blog-image">
              <img src="images/azmi.jpg" alt="UI Design Trends" />
              <div class="blog-category">UI/UX Design</div>
            </div>
            <div class="blog-content">
              <div class="blog-meta">
                <span class="blog-date"
                  ><i class="fas fa-calendar"></i> 10 Januari 2024</span
                >
                <span class="blog-read-time"
                  ><i class="fas fa-clock"></i> 7 min read</span
                >
              </div>
              <h2>Tren UI Design 2024: Apa yang Perlu Diketahui</h2>
              <p>
                Dunia design UI terus berkembang. Mari kita lihat tren-tren
                terbaru yang akan mendominasi tahun 2024 dan bagaimana
                mengimplementasikannya dalam proyek Anda.
              </p>
              <div class="blog-tags">
                <span class="tag">UI Design</span>
                <span class="tag">Trends</span>
                <span class="tag">2024</span>
              </div>
              <a href="#" class="blog-read-more"
                >Baca Selengkapnya <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </article>

          <article class="blog-post" data-category="mobile">
            <div class="blog-image">
              <img src="images/azmi.jpg" alt="Flutter vs React Native" />
              <div class="blog-category">Mobile Development</div>
            </div>
            <div class="blog-content">
              <div class="blog-meta">
                <span class="blog-date"
                  ><i class="fas fa-calendar"></i> 5 Januari 2024</span
                >
                <span class="blog-read-time"
                  ><i class="fas fa-clock"></i> 10 min read</span
                >
              </div>
              <h2>Flutter vs React Native: Mana yang Lebih Baik?</h2>
              <p>
                Perbandingan mendalam antara dua framework mobile development
                terpopuler. Kelebihan, kekurangan, dan kapan sebaiknya
                menggunakan masing-masing.
              </p>
              <div class="blog-tags">
                <span class="tag">Flutter</span>
                <span class="tag">React Native</span>
                <span class="tag">Mobile</span>
              </div>
              <a href="#" class="blog-read-more"
                >Baca Selengkapnya <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </article>

          <article class="blog-post" data-category="tutorial">
            <div class="blog-image">
              <img src="images/azmi.jpg" alt="REST API Tutorial" />
              <div class="blog-category">Tutorial</div>
            </div>
            <div class="blog-content">
              <div class="blog-meta">
                <span class="blog-date"
                  ><i class="fas fa-calendar"></i> 28 Desember 2023</span
                >
                <span class="blog-read-time"
                  ><i class="fas fa-clock"></i> 15 min read</span
                >
              </div>
              <h2>Membuat REST API dengan Node.js dan Express</h2>
              <p>
                Tutorial lengkap untuk membuat REST API dari nol menggunakan
                Node.js dan Express. Termasuk authentication, validation, dan
                best practices.
              </p>
              <div class="blog-tags">
                <span class="tag">Node.js</span>
                <span class="tag">Express</span>
                <span class="tag">API</span>
              </div>
              <a href="#" class="blog-read-more"
                >Baca Selengkapnya <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </article>

          <article class="blog-post" data-category="tips">
            <div class="blog-image">
              <img src="images/azmi.jpg" alt="Developer Productivity" />
              <div class="blog-category">Tips & Tricks</div>
            </div>
            <div class="blog-content">
              <div class="blog-meta">
                <span class="blog-date"
                  ><i class="fas fa-calendar"></i> 20 Desember 2023</span
                >
                <span class="blog-read-time"
                  ><i class="fas fa-clock"></i> 6 min read</span
                >
              </div>
              <h2>10 Tips Meningkatkan Produktivitas Developer</h2>
              <p>
                Kumpulan tips dan trik untuk meningkatkan produktivitas sebagai
                developer. Dari tools yang tepat hingga workflow yang efisien.
              </p>
              <div class="blog-tags">
                <span class="tag">Productivity</span>
                <span class="tag">Tools</span>
                <span class="tag">Workflow</span>
              </div>
              <a href="#" class="blog-read-more"
                >Baca Selengkapnya <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </article>

          <article class="blog-post" data-category="web-development">
            <div class="blog-image">
              <img src="images/azmi.jpg" alt="CSS Grid Layout" />
              <div class="blog-category">Web Development</div>
            </div>
            <div class="blog-content">
              <div class="blog-meta">
                <span class="blog-date"
                  ><i class="fas fa-calendar"></i> 15 Desember 2023</span
                >
                <span class="blog-read-time"
                  ><i class="fas fa-clock"></i> 8 min read</span
                >
              </div>
              <h2>Menguasai CSS Grid Layout untuk Layout Modern</h2>
              <p>
                CSS Grid adalah tool yang powerful untuk membuat layout web
                modern. Pelajari cara menggunakan CSS Grid dengan contoh praktis
                dan real-world use cases.
              </p>
              <div class="blog-tags">
                <span class="tag">CSS</span>
                <span class="tag">Grid</span>
                <span class="tag">Layout</span>
              </div>
              <a href="#" class="blog-read-more"
                >Baca Selengkapnya <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </article>
        </div>

        <!-- Pagination -->
        <div class="pagination">
          <button class="pagination-btn" disabled>
            <i class="fas fa-chevron-left"></i> Previous
          </button>
          <div class="pagination-numbers">
            <button class="pagination-number active">1</button>
            <button class="pagination-number">2</button>
            <button class="pagination-number">3</button>
            <span class="pagination-dots">...</span>
            <button class="pagination-number">10</button>
          </div>
          <button class="pagination-btn">
            Next <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </section>

    <!-- Newsletter Subscription -->
    <section class="newsletter-section">
      <div class="container">
        <div class="newsletter-content">
          <h2>Berlangganan Newsletter</h2>
          <p>
            Dapatkan artikel terbaru dan tips menarik langsung di inbox Anda
          </p>
          <form class="newsletter-form">
            <div class="newsletter-input-group">
              <input type="email" placeholder="Masukkan email Anda" required />
              <button type="submit" class="btn btn-primary">
                Berlangganan
              </button>
            </div>
            <p class="newsletter-privacy">
              Kami menghargai privasi Anda. Tidak ada spam, hanya konten
              berkualitas.
            </p>
          </form>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>Muhammad Azmi</h3>
            <p>
              Web Developer & UI/UX Designer yang berpengalaman dalam
              menciptakan solusi digital inovatif.
            </p>
            <div class="footer-social">
              <a href="#" class="social-link"><i class="fab fa-github"></i></a>
              <a href="#" class="social-link"
                ><i class="fab fa-linkedin"></i
              ></a>
              <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
              <a href="#" class="social-link"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-section">
            <h4>Menu</h4>
            <ul class="footer-links">
              <li><a href="index.html">Beranda</a></li>
              <li><a href="about.html">Tentang</a></li>
              <li><a href="portfolio.html">Portfolio</a></li>
              <li><a href="contact.html">Kontak</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Layanan</h4>
            <ul class="footer-links">
              <li><a href="services.html">Web Development</a></li>
              <li><a href="services.html">UI/UX Design</a></li>
              <li><a href="services.html">Mobile App</a></li>
              <li><a href="services.html">Konsultasi</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Kontak</h4>
            <div class="contact-info">
              <p><i class="fas fa-envelope"></i> <EMAIL></p>
              <p><i class="fas fa-phone"></i> +62 822 8629 3850</p>
              <p><i class="fas fa-map-marker-alt"></i> Pekanbaru, Indonesia</p>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Muhammad Azmi. Semua hak dilindungi.</p>
        </div>
      </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top">
      <i class="fas fa-chevron-up"></i>
    </button>

    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/blog.js"></script>
  </body>
</html>
