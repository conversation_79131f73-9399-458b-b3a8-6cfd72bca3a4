/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #3b82f6;
  --secondary-color: #1e40af;
  --accent-color: #f59e0b;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-dark: #111827;
  --border-color: #e5e7eb;
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

[data-theme="dark"] {
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-light: #9ca3af;
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-dark: #000000;
  --border-color: #374151;
}

body {
  font-family: "Poppins", sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: var(--transition);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Loading Screen */
#loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease;
}

.loader {
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: var(--transition);
}

[data-theme="dark"] .navbar {
  background: rgba(17, 24, 39, 0.95);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo a {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  margin: 3px 0;
  transition: var(--transition);
}

.theme-toggle {
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: var(--transition);
}

.theme-toggle:hover {
  background: var(--primary-color);
  color: white;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 100px 0 50px;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e5e7eb" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.highlight {
  color: var(--primary-color);
  position: relative;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.hero-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.8;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  border: 2px solid transparent;
  cursor: pointer;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.hero-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition);
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-3px);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container {
  position: relative;
  width: 400px;
  height: 400px;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 5px solid var(--primary-color);
  box-shadow: var(--shadow-heavy);
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  color: var(--primary-color);
  font-size: 1.5rem;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Stats Section */
.stats {
  padding: 80px 0;
  background: var(--bg-secondary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  text-align: center;
}

.stat-item {
  padding: 2rem;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 500;
}

/* Sections */
.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.section-subtitle {
  text-align: center;
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-bottom: 3rem;
}

/* Featured Work */
.featured-work {
  padding: 80px 0;
}

.work-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.work-item {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.work-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.work-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.work-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.work-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.work-item:hover .work-overlay {
  opacity: 1;
}

.work-item:hover .work-image img {
  transform: scale(1.1);
}

.work-link {
  color: white;
  font-size: 1.5rem;
  text-decoration: none;
  padding: 15px;
  border: 2px solid white;
  border-radius: 50%;
  transition: var(--transition);
}

.work-link:hover {
  background: white;
  color: var(--primary-color);
}

.work-content {
  padding: 1.5rem;
}

.work-content h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.work-content p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.work-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.text-center {
  text-align: center;
}

/* Utility Classes */
.lead {
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

/* Back to Top */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: none;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: var(--transition);
  z-index: 1000;
}

.back-to-top:hover {
  background: var(--secondary-color);
  transform: translateY(-3px);
}

.back-to-top.show {
  display: flex;
}

/* Footer */
.footer {
  background: var(--bg-dark);
  color: var(--text-light);
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  color: white;
  margin-bottom: 1rem;
}

.footer-section p {
  line-height: 1.8;
  margin-bottom: 1rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social .social-link {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
}

.footer-social .social-link:hover {
  background: var(--primary-color);
  color: white;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: var(--text-light);
  text-decoration: none;
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--primary-color);
}

.contact-info p {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  text-align: center;
}

/* Page Header */
.page-header {
  padding: 120px 0 80px;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--secondary-color) 100%
  );
  color: white;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* About Page Styles */
.about-content {
  padding: 80px 0;
}

.about-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  align-items: center;
}

.about-image {
  position: relative;
}

.about-image img {
  width: 100%;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

.image-decoration {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.1;
  z-index: -1;
}

.about-text h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.about-highlights {
  margin-top: 2rem;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.highlight-item i {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-top: 0.25rem;
}

.highlight-item h4 {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.highlight-item p {
  color: var(--text-secondary);
}

/* Personal Info */
.personal-info {
  padding: 80px 0;
  background: var(--bg-secondary);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.info-section h3 {
  font-size: 1.8rem;
  margin-bottom: 2rem;
  color: var(--text-primary);
}

.info-list {
  space-y: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.info-label {
  font-weight: 600;
  color: var(--text-primary);
}

.info-value {
  color: var(--text-secondary);
}

.interests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.interest-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.interest-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.interest-item i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.interest-item span {
  font-weight: 500;
  color: var(--text-primary);
}

/* Values Section */
.values {
  padding: 80px 0;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.value-item {
  text-align: center;
  padding: 2rem;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.value-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.value-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.value-icon i {
  font-size: 2rem;
  color: white;
}

.value-item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.value-item p {
  color: var(--text-secondary);
  line-height: 1.8;
}

/* CTA Section */
.cta {
  padding: 80px 0;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--secondary-color) 100%
  );
  color: white;
  text-align: center;
}

.cta-content h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta .btn-primary {
  background: white;
  color: var(--primary-color);
}

.cta .btn-primary:hover {
  background: var(--bg-secondary);
}

.cta .btn-secondary {
  border-color: white;
  color: white;
}

.cta .btn-secondary:hover {
  background: white;
  color: var(--primary-color);
}

/* Portfolio Styles */
.portfolio-filter {
  padding: 40px 0;
  background: var(--bg-secondary);
}

.filter-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 10px 20px;
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.portfolio-grid {
  padding: 80px 0;
}

.portfolio-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.portfolio-item {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-heavy);
}

.portfolio-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-info {
  text-align: center;
  color: white;
  padding: 2rem;
}

.portfolio-info h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.portfolio-info p {
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.portfolio-links {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.portfolio-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition);
}

.portfolio-link:hover {
  background: white;
  color: var(--primary-color);
  transform: scale(1.1);
}

.portfolio-content {
  padding: 1.5rem;
}

.portfolio-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.portfolio-category {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.portfolio-date {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.portfolio-content h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.portfolio-content p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.portfolio-tech {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tech-tag {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Skills Page Styles */
.technical-skills {
  padding: 80px 0;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
}

.skill-category h3 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--text-primary);
  text-align: center;
  position: relative;
}

.skill-category h3::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: var(--primary-color);
}

.skills-list {
  space-y: 1.5rem;
}

.skill-item {
  margin-bottom: 1.5rem;
}

.skill-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.skill-name {
  font-weight: 600;
  color: var(--text-primary);
}

.skill-percentage {
  color: var(--primary-color);
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: 10px;
  width: 0%;
  transition: width 1.5s ease-in-out;
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.soft-skills {
  padding: 80px 0;
  background: var(--bg-secondary);
}

.soft-skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.soft-skill-item {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.soft-skill-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.soft-skill-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.soft-skill-icon i {
  font-size: 2rem;
  color: white;
}

.soft-skill-item h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.soft-skill-item p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Contact Page Styles */
.contact-section {
  padding: 80px 0;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-info-section h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.contact-description {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.8;
}

.contact-methods {
  margin-bottom: 3rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.contact-method:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-icon i {
  color: white;
  font-size: 1.2rem;
}

.contact-details h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.contact-details p {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.contact-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.contact-link:hover {
  color: var(--secondary-color);
}

.social-media h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.social-links {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.social-links .social-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  text-decoration: none;
  color: var(--text-primary);
  transition: var(--transition);
}

.social-links .social-link:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Contact Form */
.contact-form-section {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

.contact-form h2 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.contact-form > p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: 1rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
}

.form-group input.valid,
.form-group select.valid,
.form-group textarea.valid {
  border-color: #10b981;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.checkbox-group {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  position: relative;
  flex-shrink: 0;
  transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
}

.privacy-link {
  color: var(--primary-color);
  text-decoration: none;
}

.privacy-link:hover {
  text-decoration: underline;
}

.btn-submit {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.btn-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.field-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* FAQ Section */
.faq-section {
  padding: 80px 0;
  background: var(--bg-secondary);
}

.faq-grid {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  box-shadow: var(--shadow-light);
  overflow: hidden;
  transition: var(--transition);
}

.faq-item:hover {
  box-shadow: var(--shadow-medium);
}

.faq-question {
  padding: 1.5rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-primary);
  transition: var(--transition);
}

.faq-question:hover {
  background: var(--bg-secondary);
}

.faq-question h3 {
  font-size: 1.1rem;
  color: var(--text-primary);
  margin: 0;
}

.faq-question i {
  color: var(--primary-color);
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-answer p {
  padding: 0 1.5rem 1.5rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Services Page Styles */
.services-section {
  padding: 80px 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.service-card {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  position: relative;
  text-align: center;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-heavy);
}

.service-card.featured {
  border: 2px solid var(--primary-color);
  transform: scale(1.05);
}

.featured-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.service-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.service-icon i {
  font-size: 2rem;
  color: white;
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.service-card > p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.service-features {
  list-style: none;
  margin-bottom: 2rem;
  text-align: left;
}

.service-features li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

.service-features i {
  color: var(--primary-color);
  font-size: 0.8rem;
}

.service-price {
  margin-bottom: 2rem;
  text-align: center;
}

.price-from {
  display: block;
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.price-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* Process Section */
.process-section {
  padding: 80px 0;
  background: var(--bg-secondary);
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.process-step {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.step-number {
  width: 60px;
  height: 60px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 700;
  flex-shrink: 0;
}

.step-content h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.step-content p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Experience Page Styles */
.experience-section {
  padding: 80px 0;
}

.experience-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.tab-btn {
  padding: 12px 24px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.tab-btn:hover,
.tab-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--border-color);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 3rem;
  display: flex;
  align-items: flex-start;
  gap: 2rem;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-date {
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  white-space: nowrap;
  position: relative;
  z-index: 2;
}

.timeline-content {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  flex: 1;
  position: relative;
}

.timeline-content h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.timeline-content h4 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-weight: 600;
}

.timeline-content > p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.timeline-achievements {
  list-style: none;
  margin-bottom: 1.5rem;
}

.timeline-achievements li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.5rem;
}

.timeline-achievements li::before {
  content: "▸";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

.timeline-skills {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.skill-tag {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Certifications Grid */
.certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.cert-item {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  text-align: center;
  transition: var(--transition);
}

.cert-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.cert-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.cert-icon i {
  font-size: 1.5rem;
  color: white;
}

.cert-item h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.cert-item p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.cert-date {
  background: var(--bg-secondary);
  color: var(--primary-color);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Blog Page Styles */
.blog-filter {
  padding: 40px 0;
  background: var(--bg-secondary);
}

.blog-section {
  padding: 80px 0;
}

.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.blog-post {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.blog-post:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.blog-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.blog-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.blog-post:hover .blog-image img {
  transform: scale(1.05);
}

.blog-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.blog-content {
  padding: 2rem;
}

.blog-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.blog-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.blog-content h2 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  line-height: 1.4;
}

.blog-content > p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.blog-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.blog-read-more {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.blog-read-more:hover {
  color: var(--secondary-color);
}

.blog-read-more i {
  transition: transform 0.3s ease;
}

.blog-read-more:hover i {
  transform: translateX(5px);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 3rem;
}

.pagination-btn {
  padding: 10px 20px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.pagination-number {
  width: 40px;
  height: 40px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.pagination-number:hover,
.pagination-number.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-dots {
  color: var(--text-secondary);
  font-weight: bold;
}

/* Newsletter Section */
.newsletter-section {
  padding: 80px 0;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
}

.newsletter-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-content h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.newsletter-content > p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.newsletter-form {
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-input-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.newsletter-input-group input {
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.newsletter-input-group .btn {
  white-space: nowrap;
}

.newsletter-privacy {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0;
}

/* Gallery Page Styles */
.gallery-filter {
  padding: 40px 0;
  background: var(--bg-secondary);
}

.gallery-section {
  padding: 80px 0;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.gallery-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--border-radius);
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition);
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-info {
  text-align: center;
  color: white;
  padding: 2rem;
}

.gallery-info h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.gallery-info p {
  margin-bottom: 1rem;
  opacity: 0.9;
}

.gallery-zoom {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.gallery-zoom:hover {
  background: white;
  color: var(--primary-color);
  transform: scale(1.1);
}

.gallery-load-more {
  text-align: center;
  margin-top: 3rem;
}

.gallery-stats {
  padding: 80px 0;
  background: var(--bg-secondary);
}

/* Testimonials Page Styles */
.featured-testimonial {
  padding: 80px 0;
  background: var(--bg-secondary);
}

.testimonial-card {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.testimonial-card.featured {
  max-width: 800px;
  margin: 0 auto;
  padding: 3rem;
  text-align: center;
}

.quote-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.testimonial-text {
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--text-primary);
  margin-bottom: 2rem;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.testimonial-author img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h4 {
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.author-info p {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.rating {
  display: flex;
  gap: 0.25rem;
}

.rating i {
  color: #fbbf24;
  font-size: 1rem;
}

.testimonials-section {
  padding: 80px 0;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.testimonial-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.testimonial-header img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.testimonial-header .author-info h4 {
  font-size: 1.1rem;
}

.testimonial-header .author-info p {
  font-size: 0.9rem;
}

.testimonials-grid .testimonial-text {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-style: normal;
}

.testimonial-project {
  margin-top: 1rem;
}

.project-tag {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Client Logos */
.client-logos {
  padding: 80px 0;
  background: var(--bg-secondary);
}

.logos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  align-items: center;
}

.logo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  transition: var(--transition);
  height: 100px;
}

.logo-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-light);
}

.logo-item img {
  max-width: 100%;
  max-height: 60px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: var(--transition);
}

.logo-item:hover img {
  filter: grayscale(0%);
}

.testimonial-stats {
  padding: 80px 0;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
}

.testimonial-stats .stat-number {
  color: white;
}
