// Blog page functionality

document.addEventListener('DOMContentLoaded', function() {
    initializeBlogFilter();
    initializePagination();
    initializeNewsletterForm();
});

// Blog filter functionality
function initializeBlogFilter() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const blogPosts = document.querySelectorAll('.blog-post');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter blog posts
            blogPosts.forEach(post => {
                const category = post.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    post.style.display = 'block';
                    setTimeout(() => {
                        post.style.opacity = '1';
                        post.style.transform = 'translateY(0)';
                    }, 100);
                } else {
                    post.style.opacity = '0';
                    post.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        post.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
}

// Pagination functionality
function initializePagination() {
    const paginationNumbers = document.querySelectorAll('.pagination-number');
    const paginationBtns = document.querySelectorAll('.pagination-btn');

    paginationNumbers.forEach(number => {
        number.addEventListener('click', function() {
            if (!this.classList.contains('active')) {
                // Remove active from all numbers
                paginationNumbers.forEach(num => num.classList.remove('active'));
                // Add active to clicked number
                this.classList.add('active');
                
                // Simulate page load
                showNotification('Memuat halaman ' + this.textContent + '...', 'info');
            }
        });
    });

    paginationBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.disabled) {
                const isNext = this.textContent.includes('Next');
                showNotification(isNext ? 'Memuat halaman berikutnya...' : 'Memuat halaman sebelumnya...', 'info');
            }
        });
    });
}

// Newsletter form functionality
function initializeNewsletterForm() {
    const form = document.querySelector('.newsletter-form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[type="email"]').value;
            
            if (email) {
                // Simulate subscription
                showNotification('Terima kasih! Anda telah berlangganan newsletter.', 'success');
                this.reset();
            }
        });
    }
}
