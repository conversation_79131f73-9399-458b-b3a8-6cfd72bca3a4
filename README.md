# Situs Web Pribadi (Personal Website)

Situs web pribadi yang modern dan responsif dengan 10 halaman lengkap, dibangun menggunakan HTML, CSS, dan <PERSON> murni.

## 🌟 Fitur Utama

- **10 Halaman Lengkap**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Port<PERSON>lio, Ke<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ana<PERSON>, Blog, <PERSON>ri, Testimoni, dan <PERSON>
- **Design Responsif**: Optimal di semua perangkat (desktop, tablet, mobile)
- **Dark/Light Mode**: Toggle tema gelap dan terang
- **Animasi Interaktif**: Smooth scrolling, hover effects, dan loading animations
- **Portfolio Filter**: Filter proyek berdasarkan kategori
- **Blog dengan Pagination**: Sistem blog dengan filter dan pagination
- **Galeri dengan Lightbox**: Galeri foto dengan efek lightbox
- **Form Kontak**: Formulir kontak dengan validasi
- **SEO Friendly**: Struktur HTML yang optimal untuk SEO

## 📁 Struktur Proyek

```
tugas2-pweb/
├── index.html              # Halaman beranda
├── about.html              # Halaman tentang
├── portfolio.html          # Halaman portfolio
├── skills.html             # Halaman keahlian
├── experience.html         # Halaman pengalaman
├── services.html           # Halaman layanan
├── blog.html               # Halaman blog
├── gallery.html            # Halaman galeri
├── testimonials.html       # Halaman testimoni
├── contact.html            # Halaman kontak
├── css/
│   ├── style.css           # CSS utama
│   └── responsive.css      # CSS responsif
├── js/
│   ├── main.js             # JavaScript utama
│   ├── animations.js       # Animasi dan efek
│   ├── portfolio.js        # Fungsi portfolio
│   ├── experience.js       # Fungsi experience
│   ├── contact.js          # Fungsi contact
│   ├── blog.js             # Fungsi blog
│   └── gallery.js          # Fungsi gallery
├── images/                 # Folder gambar
└── README.md               # Dokumentasi
```

## 🚀 Cara Menjalankan

1. **Clone atau Download** proyek ini
2. **Buka file `index.html`** di browser web
3. **Atau gunakan Live Server** untuk development:
   ```bash
   # Jika menggunakan VS Code dengan Live Server extension
   # Klik kanan pada index.html > Open with Live Server
   ```

## 📱 Halaman yang Tersedia

### 1. **Beranda (index.html)**
- Hero section dengan animasi
- Statistik counter
- Featured work
- Call-to-action

### 2. **Tentang (about.html)**
- Informasi pribadi
- Nilai-nilai dan prinsip
- Minat dan hobi
- Highlight keahlian

### 3. **Portfolio (portfolio.html)**
- Filter proyek berdasarkan kategori
- Grid layout responsif
- Detail proyek dengan teknologi
- Link ke demo dan source code

### 4. **Keahlian (skills.html)**
- Progress bar untuk technical skills
- Soft skills dengan icon
- Kategorisasi keahlian
- Animasi on scroll

### 5. **Pengalaman (experience.html)**
- Timeline pengalaman kerja
- Riwayat pendidikan
- Sertifikasi dan achievement
- Tab navigation

### 6. **Layanan (services.html)**
- Daftar layanan yang ditawarkan
- Pricing dan fitur
- Proses kerja
- Call-to-action

### 7. **Blog (blog.html)**
- Filter artikel berdasarkan kategori
- Pagination
- Newsletter subscription
- Reading time estimation

### 8. **Galeri (gallery.html)**
- Masonry grid layout
- Lightbox untuk preview
- Filter berdasarkan kategori
- Load more functionality

### 9. **Testimoni (testimonials.html)**
- Featured testimonial
- Grid testimoni klien
- Rating system
- Client logos

### 10. **Kontak (contact.html)**
- Formulir kontak dengan validasi
- Informasi kontak
- FAQ section
- Social media links

## 🎨 Teknologi yang Digunakan

- **HTML5**: Struktur semantic dan modern
- **CSS3**: 
  - Flexbox dan Grid Layout
  - CSS Variables untuk theming
  - Animations dan Transitions
  - Media queries untuk responsivitas
- **JavaScript (Vanilla)**:
  - DOM manipulation
  - Event handling
  - Intersection Observer API
  - Local Storage untuk theme
- **Font Awesome**: Icons
- **Google Fonts**: Typography (Poppins)

## ✨ Fitur JavaScript

### Navigasi
- Mobile hamburger menu
- Smooth scrolling
- Active link highlighting
- Sticky navigation

### Theme System
- Dark/Light mode toggle
- Preference saved in localStorage
- Smooth theme transitions

### Animasi
- Scroll-triggered animations
- Counter animations
- Parallax effects
- Loading screen

### Interaktivitas
- Portfolio filtering
- Blog filtering
- Gallery lightbox
- Form validation
- FAQ accordion

## 🎯 Customization

### Mengubah Konten
1. Edit file HTML untuk mengubah teks dan struktur
2. Ganti gambar di folder `images/`
3. Update informasi kontak dan social media

### Mengubah Styling
1. Edit CSS variables di `css/style.css` untuk mengubah warna
2. Modify layout di section yang diinginkan
3. Tambah custom animations di `css/style.css`

### Menambah Functionality
1. Tambah JavaScript functions di `js/main.js`
2. Buat file JS terpisah untuk fitur kompleks
3. Import di HTML dengan tag `<script>`

## 📱 Responsivitas

Website ini fully responsive dengan breakpoints:
- **Desktop**: 1200px+
- **Laptop**: 992px - 1199px
- **Tablet**: 768px - 991px
- **Mobile**: 320px - 767px

## 🔧 Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 📄 License

Proyek ini dibuat untuk keperluan tugas dan pembelajaran. Silakan gunakan sebagai referensi atau template.

## 👨‍💻 Author

**John Doe**
- Email: <EMAIL>
- LinkedIn: [linkedin.com/in/johndoe](https://linkedin.com/in/johndoe)
- GitHub: [github.com/johndoe](https://github.com/johndoe)

## 🤝 Contributing

Jika Anda ingin berkontribusi:
1. Fork repository ini
2. Buat branch untuk fitur baru
3. Commit perubahan Anda
4. Push ke branch
5. Buat Pull Request

## 📝 Catatan

- Ganti semua placeholder content dengan informasi asli
- Tambahkan gambar real di folder `images/`
- Update informasi kontak dan social media
- Customize warna dan styling sesuai preferensi
- Test di berbagai browser dan device

---

**Happy Coding! 🚀**
